@use 'assets/sass/variables' as *;

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dialog-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 34px 5px 24px;
}

.filters {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 10px;
}

.mat-dialog-title {
    .filters {
        text-transform: initial;
    }
}

.scroll-right-padding {
    padding-right: 11px;
}

.remove-margin-bottom {
    margin-bottom: 0;
}

.height-12px {
    height: 12px;
}

.red-color {
    color: $red !important;
}

.field-copies-dialog-table {
    width: 100%;
    box-shadow: none;

    th.mat-header-cell:last-of-type {
        width: 117px;
        text-align: right;
    }

    .mat-column-select {
        width: 210px;
        text-align: right;
    }

    .mat-header-cell {
        text-transform: uppercase;
    }

    .file-over {
        filter: drop-shadow(0 0px 8px rgba(0, 0, 0, 0.2));
    }

    .upload-blocked {
        background-color: #ededed;
    }
}

.mat-dialog-content {
    max-height: 70vh;
    min-height: 40vh;
}

.btn-border-n {
    border-radius: 0;
    padding-top: 22px;
    padding-bottom: 22px;
}

.purchase-container {
    display: flex;

    .text {
        font-size: 14px;
        margin-left: 5px;
    }
}

.purchase-all-btn,
.unavailable-btn,
.upload-btn,
.availability-selector {
    width: 210px;
}

.purchase-all-btn,
.upload-btn,
.unavailable-btn {
    outline: none;
    text-transform: uppercase;
    height: 32px;
    background: #b4b4b4;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 8px;
    font-size: 14px;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.upload-btn {
    width: 110px;
    background-color: #748ece;

    &:disabled {
        background: #b4b4b4;
        pointer-events: none;
    }
}

.drop {
    &__status {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 136px;
        background-color: transparent;
    }

    &__icon-status {
        width: 30px;
        height: 30px;
    }
}

.mat-checkbox-label {
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 18px;
    color: #333333;
}

.mat-ripple-element {
    background: #999 !important;
}

.purchase-all-errors {
    padding: 10px 24px;
}

.hidden-button {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.purchase-all-btn {
    background-color: #748ece;
    color: white;
    box-sizing: border-box;

    &:disabled {
        opacity: 0.7;
    }
}

.unavailable-btn {
    font-family: 'ArialBold', sans-serif;

    &:disabled {
        pointer-events: none;
    }
}

.error {
    padding: 16px;
    display: none;
    height: 0;
    border: none;
}

.visible {
    display: table-row;
    height: 100%;
    transition-duration: 12s;
    transition-property: height;
}

.table-errors table {
    td.mat-cell {
        position: relative;
        box-sizing: border-box;
        padding: 12px 8px 12px 24px;
        vertical-align: center;
        font-family: Arial;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        height: 75px;
        min-height: 75px;

        &.expanded {
            z-index: 1;
            padding: 0 24px 12px 24px;
            border-radius: 0;
            top: -2px;
            height: 38px;
            min-height: 38px;
            background: #fff;
        }
    }

    .mat-column {
        &-expandedDetail {
            position: relative;

            &:after {
                content: '';
                position: absolute;
                left: 0;
                width: 100%;
                height: 15px;
                border-bottom: 1px solid rgba(204, 204, 204, 0.3);
                background: #fff;
            }
        }
    }
}

.empty-disclaimer {
    display: flex;
    justify-content: center;
    padding-top: 50px;
    color: #999;
    font-size: 14px;
}

.hidden {
    display: none;
}

.icon-with-text {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-and-tour-button {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
}

.reset-filed-copies-tour {
    width: 15px;
    height: 15px;
    color: $gray;
    cursor: pointer;
    opacity: 0.7;
}

::ng-deep .field-copies-dialog-table {
    td.mat-cell {
        padding-top: 5px;
        padding-bottom: 5px;
    }

    .mat-header-cell.mat-column {
        &-upload {
            text-align: center;
            padding-right: 10px;
        }

        &-purchase {
            padding-left: 10px;
        }
    }

    .mat-cell.mat-column {
        &-upload {
            padding-right: 10px;
            width: 136px;
        }

        &-purchase {
            padding-left: 10px;
        }
    }
}

@media (max-height: 660px) {
    .mat-dialog-content {
        min-height: 60vh;
    }
}

@media (max-height: 560px) {
    .mat-dialog-content {
        height: 55vh;
    }
}

.items-group {
    $bg-color: var(--bg-color);
    $border-color: var(--border-color);
    background-color: $bg-color;
    border-width: 2px;
    box-shadow: 0px 4px 4px 0px #0000000F;

    td {
        border-color: $border-color;
        border-bottom-style: none;
    }

    td:first-child {
        border-left-style: solid;
    }

    td:last-child {
        border-right-style: solid;
    }

    &--first {
        td {
            border-top-style: solid;
        }

        td:first-child {
            border-top-left-radius: $primary-border-radius;
        }

        td:last-child {
            border-top-right-radius: $primary-border-radius;
        }
    }

    &--last {
        td {
            border-bottom-style: solid;
        }

        td:first-child {
            border-bottom-left-radius: $primary-border-radius;
        }

        td:last-child {
            border-bottom-right-radius: $primary-border-radius;
        }
    }
}
